version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trading-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n_trading
      POSTGRES_USER: n8n_user
      POSTGRES_PASSWORD: secure_password_123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
    ports:
      - "5432:5432"
    networks:
      - trading-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n_user -d n8n_trading"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # n8n Workflow Engine
  n8n:
    image: n8nio/n8n:latest
    container_name: trading-n8n
    restart: unless-stopped
    environment:
      # Database Configuration
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n_trading
      DB_POSTGRESDB_USER: n8n_user
      DB_POSTGRESDB_PASSWORD: secure_password_123

      # n8n Configuration
      N8N_HOST: localhost
      N8N_PORT: 5678
      N8N_PROTOCOL: http
      WEBHOOK_URL: http://localhost:5678/

      # Security
      N8N_ENCRYPTION_KEY: your_encryption_key_here_32_chars
      N8N_USER_MANAGEMENT_DISABLED: "true"
      
      # Performance
      EXECUTIONS_PROCESS: main
      EXECUTIONS_DATA_SAVE_ON_ERROR: all
      EXECUTIONS_DATA_SAVE_ON_SUCCESS: all
      EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS: true
      
      # Logging
      N8N_LOG_LEVEL: info
      N8N_LOG_OUTPUT: console

      # Timezone
      GENERIC_TIMEZONE: Europe/Istanbul
      TZ: Europe/Istanbul
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/workflows:ro
    networks:
      - trading-network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5678/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Trading System Application
  trading-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trading-app
    restart: unless-stopped
    environment:
      # Database Configuration
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n_trading
      DB_POSTGRESDB_USER: n8n_user
      DB_POSTGRESDB_PASSWORD: secure_password_123

      # n8n Configuration
      N8N_HOST: n8n
      N8N_PORT: 5678
      N8N_PROTOCOL: http

      # System Configuration
      NODE_ENV: production
      LOG_LEVEL: info
      TZ: Europe/Istanbul

      # Trading Configuration
      DEFAULT_RISK_LEVEL: medium
      MAX_POSITION_SIZE: 0.1
      STOP_LOSS_PERCENTAGE: 5
      TAKE_PROFIT_PERCENTAGE: 15

      # Security
      WEBHOOK_SECRET: your_webhook_secret_here
      API_RATE_LIMIT: 100
    ports:
      - "3000:3000"
    volumes:
      - app_logs:/app/logs
      - app_backups:/app/backups
    networks:
      - trading-network
    depends_on:
      postgres:
        condition: service_healthy
      n8n:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis for Caching (Optional)
  redis:
    image: redis:7-alpine
    container_name: trading-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  n8n_data:
    driver: local
  app_logs:
    driver: local
  app_backups:
    driver: local
  redis_data:
    driver: local

networks:
  trading-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
